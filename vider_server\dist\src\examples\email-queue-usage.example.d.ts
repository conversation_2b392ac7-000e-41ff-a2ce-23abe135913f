import { EnhancedEmailService } from '../modules/email-queue/enhanced-email.service';
export declare class EmailQueueUsageExample {
    private enhancedEmailService;
    constructor(enhancedEmailService: EnhancedEmailService);
    sendSingleEmail(): Promise<void>;
    sendBulkEmails(): Promise<void>;
    sendTemplatedEmail(): Promise<void>;
    sendUrgentEmail(): Promise<void>;
    sendWithCustomSMTP(): Promise<void>;
    monitorQueue(): Promise<void>;
    sendMarketingCampaign(): Promise<void>;
    manageQueue(): Promise<void>;
    sendLegacyEmail(): Promise<void>;
}
