import { EnhancedEmailService } from './enhanced-email.service';
import { EmailQueueTestService } from './email-queue.test.service';
import { EmailJobData } from './email-queue.service';
export declare class EmailQueueController {
    private enhancedEmailService;
    private emailQueueTestService;
    constructor(enhancedEmailService: EnhancedEmailService, emailQueueTestService: EmailQueueTestService);
    getQueueStats(): Promise<{
        waiting: number;
        active: number;
        completed: number;
        failed: number;
        total: number;
    }>;
    sendEmail(emailData: EmailJobData): Promise<{
        message: string;
    }>;
    sendBulkEmails(emails: EmailJobData[]): Promise<{
        message: string;
    }>;
    sendTemplatedEmail(data: {
        to: string | string[];
        templatePath: string;
        templateData: any;
        subject: string;
        org?: any;
        priority?: 'low' | 'normal' | 'high' | 'critical';
    }): Promise<{
        message: string;
    }>;
    pauseQueue(): Promise<{
        message: string;
    }>;
    resumeQueue(): Promise<{
        message: string;
    }>;
    clearQueue(): Promise<{
        message: string;
    }>;
    testEmail(data: {
        to: string;
        subject: string;
        content: string;
    }): Promise<{
        message: string;
    }>;
    runEmailQueueTests(): Promise<{
        message: string;
    }>;
    testRateLimiting(): Promise<{
        message: string;
    }>;
}
