{"version": 3, "file": "email.processor.js", "sourceRoot": "", "sources": ["../../../../src/modules/email-queue/email.processor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,uCAAmF;AACnF,2CAAwC;AAExC,yCAAyC;AAIlC,IAAM,cAAc,sBAApB,MAAM,cAAc;IAIzB;QAHiB,WAAM,GAAG,IAAI,eAAM,CAAC,gBAAc,CAAC,IAAI,CAAC,CAAC;QAKxD,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAEO,qBAAqB;QAE3B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,eAAe,CAAC;YAC5C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,qCAAqC;YACrE,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,KAAK,CAAC;YAC/C,IAAI,EAAE;gBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,sBAAsB;gBACtD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,8CAA8C;aACnF;SACF,CAAC,CAAC;IACL,CAAC;IAGK,AAAN,KAAK,CAAC,eAAe,CAAC,GAAsB;QAC1C,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;QACrB,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAErD,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;YAG5D,IAAI,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;YAGxC,IAAI,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,EAAE;gBACb,gBAAgB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aAC3D;YAGD,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;gBAC9B,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC1C,OAAO;gBACP,IAAI;gBACJ,WAAW,EAAE,WAAW,IAAI,EAAE;aAC/B,CAAC;YAGF,MAAM,MAAM,GAAG,MAAM,gBAAgB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAE5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mCAAmC,GAAG,CAAC,EAAE,iBAAiB,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAG9F,MAAM,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAExB,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAC;SAEH;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAGpE,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kCAAkC,GAAG,CAAC,EAAE,cAAc,CAAC,CAAC;gBAEzE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;aACxB;YAED,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IAEO,uBAAuB,CAAC,UAAe;QAC7C,MAAM,MAAM,qBAAQ,UAAU,CAAE,CAAC;QAGjC,IAAI,MAAM,CAAC,IAAI;YAAE,OAAO,MAAM,CAAC,IAAI,CAAC;QACpC,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,EAAE;YAC9D,OAAO,MAAM,CAAC,OAAO,CAAC;SACvB;QAED,OAAO,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC5C,CAAC;IAEO,cAAc,CAAC,GAAS;;QAC9B,IAAI,MAAA,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,0CAAE,IAAI,0CAAE,IAAI,EAAE;YACzB,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI;gBAClB,CAAC,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;gBAC9C,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;SACxB;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,eAAe,CAAC;IACnD,CAAC;IAEO,gBAAgB,CAAC,KAAU;;QACjC,MAAM,YAAY,GAAG,CAAA,MAAA,KAAK,CAAC,OAAO,0CAAE,WAAW,EAAE,KAAI,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,CAAA,MAAA,KAAK,CAAC,IAAI,0CAAE,WAAW,EAAE,KAAI,EAAE,CAAC;QAElD,OAAO,CACL,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC;YACnC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACjC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC;YACjC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC1B,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAC/B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAIK,AAAN,KAAK,CAAC,WAAW,CAAC,GAAQ,EAAE,MAAW;QACrC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,aAAa,GAAG,CAAC,EAAE,yBAAyB,CAAC,CAAC;IAChE,CAAC;IAIK,AAAN,KAAK,CAAC,QAAQ,CAAC,GAAQ,EAAE,KAAY;QACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QAOxD,IAAI,GAAG,CAAC,YAAY,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,EAAE,sDAAsD,CAAC,CAAC;SAE9F;IACH,CAAC;CACF,CAAA;AA/GO;IADL,IAAA,cAAO,EAAC,YAAY,CAAC;;;;qDAmDrB;AA0CK;IADL,IAAA,uBAAgB,GAAE;;;;iDAGlB;AAIK;IADL,IAAA,oBAAa,GAAE;;6CACgB,KAAK;;8CAYpC;AApIU,cAAc;IAD1B,IAAA,gBAAS,EAAC,aAAa,CAAC;;GACZ,cAAc,CAqI1B;AArIY,wCAAc"}