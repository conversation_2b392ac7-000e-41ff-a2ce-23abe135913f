import { Job } from 'bull';
import { EmailJobData } from './email-queue.service';
export declare class EmailProcessor {
    private readonly logger;
    private transporter;
    constructor();
    private initializeTransporter;
    handleSendEmail(job: Job<EmailJobData>): Promise<{
        success: boolean;
        messageId: any;
        response: any;
    }>;
    private createCustomTransporter;
    private getFromAddress;
    private isRateLimitError;
    private delay;
    onCompleted(job: Job, result: any): Promise<void>;
    onFailed(job: Job, error: Error): Promise<void>;
}
