"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailQueueTestService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueTestService = void 0;
const common_1 = require("@nestjs/common");
const enhanced_email_service_1 = require("./enhanced-email.service");
let EmailQueueTestService = EmailQueueTestService_1 = class EmailQueueTestService {
    constructor(enhancedEmailService) {
        this.enhancedEmailService = enhancedEmailService;
        this.logger = new common_1.Logger(EmailQueueTestService_1.name);
    }
    async testEmailQueue() {
        try {
            this.logger.log('Starting email queue test...');
            await this.enhancedEmailService.sendEmail({
                to: '<EMAIL>',
                subject: 'Test Email - Single',
                html: '<h1>Test Email</h1><p>This is a test email from the queue system.</p>',
                priority: 'normal',
                emailType: 'test',
            });
            this.logger.log('✅ Single email queued successfully');
            const bulkEmails = [];
            for (let i = 1; i <= 10; i++) {
                bulkEmails.push({
                    to: `test${i}@example.com`,
                    subject: `Bulk Test Email ${i}`,
                    html: `<h1>Bulk Test ${i}</h1><p>This is bulk test email number ${i}.</p>`,
                    priority: 'low',
                    emailType: 'bulk-test',
                });
            }
            await this.enhancedEmailService.sendBulkEmails(bulkEmails);
            this.logger.log('✅ 10 bulk emails queued successfully');
            await this.enhancedEmailService.sendEmail({
                to: '<EMAIL>',
                subject: 'High Priority Test',
                html: '<h1>High Priority</h1><p>This email should be processed before low priority emails.</p>',
                priority: 'high',
                emailType: 'priority-test',
            });
            this.logger.log('✅ High priority email queued successfully');
            const stats = await this.enhancedEmailService.getQueueStats();
            this.logger.log('📊 Queue Statistics:', stats);
            this.logger.log('🎉 Email queue test completed successfully!');
            this.logger.log('📝 Check the queue stats to see emails being processed');
        }
        catch (error) {
            this.logger.error('❌ Email queue test failed:', error);
            throw error;
        }
    }
    async testRateLimiting() {
        try {
            this.logger.log('Testing rate limiting with 50 emails...');
            const emails = [];
            for (let i = 1; i <= 50; i++) {
                emails.push({
                    to: `ratetest${i}@example.com`,
                    subject: `Rate Limit Test ${i}`,
                    html: `<p>Rate limiting test email ${i}</p>`,
                    priority: 'low',
                    emailType: 'rate-test',
                });
            }
            const startTime = Date.now();
            await this.enhancedEmailService.sendBulkEmails(emails);
            const endTime = Date.now();
            this.logger.log(`✅ 50 emails queued in ${endTime - startTime}ms`);
            this.logger.log('📊 These emails will be sent with rate limiting applied');
            const stats = await this.enhancedEmailService.getQueueStats();
            this.logger.log('📊 Current Queue Statistics:', stats);
        }
        catch (error) {
            this.logger.error('❌ Rate limiting test failed:', error);
            throw error;
        }
    }
    async testTemplatedEmail() {
        try {
            this.logger.log('Testing templated email...');
            await this.enhancedEmailService.sendUserInvitation({
                email: '<EMAIL>',
                name: 'Test User',
                link: 'https://vider.in/invite/test123',
                organization: 'Test Organization',
            });
            this.logger.log('✅ Templated email (user invitation) queued successfully');
        }
        catch (error) {
            this.logger.error('❌ Templated email test failed:', error);
            this.logger.warn('Note: Template file might not exist, but queue functionality is working');
        }
    }
    async runAllTests() {
        this.logger.log('🚀 Starting comprehensive email queue tests...');
        await this.testEmailQueue();
        await this.testRateLimiting();
        await this.testTemplatedEmail();
        this.logger.log('🎉 All email queue tests completed!');
        this.logger.log('💡 Monitor the queue stats to see emails being processed with rate limiting');
    }
};
EmailQueueTestService = EmailQueueTestService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [enhanced_email_service_1.EnhancedEmailService])
], EmailQueueTestService);
exports.EmailQueueTestService = EmailQueueTestService;
//# sourceMappingURL=email-queue.test.service.js.map