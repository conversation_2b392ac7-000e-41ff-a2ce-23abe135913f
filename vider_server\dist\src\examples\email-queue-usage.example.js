"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueUsageExample = void 0;
const common_1 = require("@nestjs/common");
const enhanced_email_service_1 = require("../modules/email-queue/enhanced-email.service");
let EmailQueueUsageExample = class EmailQueueUsageExample {
    constructor(enhancedEmailService) {
        this.enhancedEmailService = enhancedEmailService;
    }
    async sendSingleEmail() {
        await this.enhancedEmailService.sendEmail({
            to: '<EMAIL>',
            subject: 'Welcome to Vider',
            html: '<h1>Welcome!</h1><p>Thank you for joining us.</p>',
            priority: 'normal',
            emailType: 'welcome',
        });
    }
    async sendBulkEmails() {
        const emails = [];
        for (let i = 1; i <= 100; i++) {
            emails.push({
                to: `user${i}@example.com`,
                subject: `Newsletter #${i}`,
                html: `<h1>Newsletter</h1><p>This is newsletter number ${i}</p>`,
                priority: 'low',
                emailType: 'newsletter',
            });
        }
        await this.enhancedEmailService.sendBulkEmails(emails);
        console.log('100 emails queued successfully with rate limiting!');
    }
    async sendTemplatedEmail() {
        await this.enhancedEmailService.sendTemplatedEmail('<EMAIL>', 'user-invited', {
            name: 'John Doe',
            organization: 'Acme Corp',
            link: 'https://vider.in/invite/abc123',
        }, 'Invitation to join | Vider', null, 'high');
    }
    async sendUrgentEmail() {
        await this.enhancedEmailService.sendEmail({
            to: '<EMAIL>',
            subject: 'URGENT: System Alert',
            html: '<h1>System Alert</h1><p>Immediate attention required.</p>',
            priority: 'critical',
            emailType: 'alert',
        });
    }
    async sendWithCustomSMTP() {
        const orgWithCustomSMTP = {
            smtp: {
                host: 'smtp.gmail.com',
                port: 587,
                auth: {
                    user: '<EMAIL>',
                    pass: 'your-app-password',
                },
                name: 'Your Organization',
            },
        };
        await this.enhancedEmailService.sendEmail({
            to: '<EMAIL>',
            subject: 'Message from Your Organization',
            html: '<p>This email is sent using custom SMTP settings.</p>',
            org: orgWithCustomSMTP,
            priority: 'normal',
            emailType: 'custom-smtp',
        });
    }
    async monitorQueue() {
        const stats = await this.enhancedEmailService.getQueueStats();
        console.log('Email Queue Statistics:', {
            waiting: stats.waiting,
            active: stats.active,
            completed: stats.completed,
            failed: stats.failed,
            total: stats.total,
        });
    }
    async sendMarketingCampaign() {
        const recipients = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];
        const htmlContent = `
      <h1>Special Offer!</h1>
      <p>Don't miss our limited-time offer.</p>
      <a href="https://vider.in/offer">Claim Now</a>
    `;
        await this.enhancedEmailService.sendMarketingEmails(recipients, 'Special Offer - Limited Time!', htmlContent);
    }
    async manageQueue() {
        await this.enhancedEmailService.pauseEmailQueue();
        console.log('Email queue paused');
        await this.enhancedEmailService.resumeEmailQueue();
        console.log('Email queue resumed');
    }
    async sendLegacyEmail() {
        await this.enhancedEmailService.sendnewMail({
            email: '<EMAIL>',
            data: {
                name: 'John Doe',
                organization: 'Acme Corp',
                link: 'https://vider.in/reset/abc123',
            },
            filePath: 'reset-password',
            subject: 'Reset Password | Vider',
            key: 'PASSWORD_RESET',
            id: 123,
        });
    }
};
EmailQueueUsageExample = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [enhanced_email_service_1.EnhancedEmailService])
], EmailQueueUsageExample);
exports.EmailQueueUsageExample = EmailQueueUsageExample;
//# sourceMappingURL=email-queue-usage.example.js.map