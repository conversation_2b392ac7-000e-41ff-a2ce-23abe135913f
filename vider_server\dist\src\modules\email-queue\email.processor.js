"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EmailProcessor_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailProcessor = void 0;
const bull_1 = require("@nestjs/bull");
const common_1 = require("@nestjs/common");
const nodemailer = require("nodemailer");
let EmailProcessor = EmailProcessor_1 = class EmailProcessor {
    constructor() {
        this.logger = new common_1.Logger(EmailProcessor_1.name);
        this.initializeTransporter();
    }
    initializeTransporter() {
        this.transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST || 'email-smtp.ap-south-1.amazonaws.com',
            port: parseInt(process.env.EMAIL_PORT || '587'),
            auth: {
                user: process.env.EMAIL_USER || 'AKIA5GHOVJDTRJ3PAQ6E',
                pass: process.env.EMAIL_PASSWORD || 'BFt/gc++ytmTt24jK/317ARm7RQPk9eS12ThV1hZ5Jgc',
            },
        });
    }
    async handleSendEmail(job) {
        const { data } = job;
        const { to, subject, html, attachments, org } = data;
        try {
            this.logger.log(`Processing email job ${job.id} for ${to}`);
            let emailTransporter = this.transporter;
            if (org === null || org === void 0 ? void 0 : org.smtp) {
                emailTransporter = this.createCustomTransporter(org.smtp);
            }
            const mailOptions = {
                from: this.getFromAddress(org),
                to: Array.isArray(to) ? to.join(', ') : to,
                subject,
                html,
                attachments: attachments || [],
            };
            const result = await emailTransporter.sendMail(mailOptions);
            this.logger.log(`Email sent successfully for job ${job.id}. Message ID: ${result.messageId}`);
            await job.progress(100);
            return {
                success: true,
                messageId: result.messageId,
                response: result.response,
            };
        }
        catch (error) {
            this.logger.error(`Failed to send email for job ${job.id}:`, error);
            if (this.isRateLimitError(error)) {
                this.logger.warn(`Rate limiting detected for job ${job.id}, will retry`);
                await this.delay(5000);
            }
            throw error;
        }
    }
    createCustomTransporter(smtpConfig) {
        const config = Object.assign({}, smtpConfig);
        if (config.name)
            delete config.name;
        if (config.service === "outlook" || config.service === "yahoo") {
            delete config.service;
        }
        return nodemailer.createTransport(config);
    }
    getFromAddress(org) {
        var _a, _b;
        if ((_b = (_a = org === null || org === void 0 ? void 0 : org.smtp) === null || _a === void 0 ? void 0 : _a.auth) === null || _b === void 0 ? void 0 : _b.user) {
            return org.smtp.name
                ? `"${org.smtp.name}" <${org.smtp.auth.user}>`
                : org.smtp.auth.user;
        }
        return process.env.FROM_EMAIL || "<EMAIL>";
    }
    isRateLimitError(error) {
        var _a, _b;
        const errorMessage = ((_a = error.message) === null || _a === void 0 ? void 0 : _a.toLowerCase()) || '';
        const errorCode = ((_b = error.code) === null || _b === void 0 ? void 0 : _b.toLowerCase()) || '';
        return (errorMessage.includes('rate limit') ||
            errorMessage.includes('throttle') ||
            errorMessage.includes('too many') ||
            errorCode.includes('rate') ||
            errorCode.includes('throttle'));
    }
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async onCompleted(job, result) {
        this.logger.log(`Email job ${job.id} completed successfully`);
    }
    async onFailed(job, error) {
        this.logger.error(`Email job ${job.id} failed:`, error);
        if (job.attemptsMade >= (job.opts.attempts || 3)) {
            this.logger.error(`Email job ${job.id} exhausted all retries. Moving to dead letter queue.`);
        }
    }
};
__decorate([
    (0, bull_1.Process)('send-email'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailProcessor.prototype, "handleSendEmail", null);
__decorate([
    (0, bull_1.OnQueueCompleted)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], EmailProcessor.prototype, "onCompleted", null);
__decorate([
    (0, bull_1.OnQueueFailed)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Error]),
    __metadata("design:returntype", Promise)
], EmailProcessor.prototype, "onFailed", null);
EmailProcessor = EmailProcessor_1 = __decorate([
    (0, bull_1.Processor)('email-queue'),
    __metadata("design:paramtypes", [])
], EmailProcessor);
exports.EmailProcessor = EmailProcessor;
//# sourceMappingURL=email.processor.js.map