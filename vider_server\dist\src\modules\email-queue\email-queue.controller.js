"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueController = void 0;
const common_1 = require("@nestjs/common");
const enhanced_email_service_1 = require("./enhanced-email.service");
const email_queue_test_service_1 = require("./email-queue.test.service");
let EmailQueueController = class EmailQueueController {
    constructor(enhancedEmailService, emailQueueTestService) {
        this.enhancedEmailService = enhancedEmailService;
        this.emailQueueTestService = emailQueueTestService;
    }
    async getQueueStats() {
        return await this.enhancedEmailService.getQueueStats();
    }
    async sendEmail(emailData) {
        await this.enhancedEmailService.sendEmail(emailData);
        return { message: 'Email queued successfully' };
    }
    async sendBulkEmails(emails) {
        await this.enhancedEmailService.sendBulkEmails(emails);
        return { message: `${emails.length} emails queued successfully` };
    }
    async sendTemplatedEmail(data) {
        await this.enhancedEmailService.sendTemplatedEmail(data.to, data.templatePath, data.templateData, data.subject, data.org, data.priority);
        return { message: 'Templated email queued successfully' };
    }
    async pauseQueue() {
        await this.enhancedEmailService.pauseEmailQueue();
        return { message: 'Email queue paused' };
    }
    async resumeQueue() {
        await this.enhancedEmailService.resumeEmailQueue();
        return { message: 'Email queue resumed' };
    }
    async clearQueue() {
        await this.enhancedEmailService.clearEmailQueue();
        return { message: 'Email queue cleared' };
    }
    async testEmail(data) {
        await this.enhancedEmailService.sendNotificationEmail(data.to, data.subject, data.content, null, 'normal');
        return { message: 'Test email queued successfully' };
    }
    async runEmailQueueTests() {
        await this.emailQueueTestService.runAllTests();
        return { message: 'Email queue tests completed. Check logs for results.' };
    }
    async testRateLimiting() {
        await this.emailQueueTestService.testRateLimiting();
        return { message: 'Rate limiting test completed. Check queue stats.' };
    }
};
__decorate([
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "getQueueStats", null);
__decorate([
    (0, common_1.Post)('send'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "sendEmail", null);
__decorate([
    (0, common_1.Post)('send-bulk'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "sendBulkEmails", null);
__decorate([
    (0, common_1.Post)('send-templated'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "sendTemplatedEmail", null);
__decorate([
    (0, common_1.Post)('pause'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "pauseQueue", null);
__decorate([
    (0, common_1.Post)('resume'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "resumeQueue", null);
__decorate([
    (0, common_1.Delete)('clear'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "clearQueue", null);
__decorate([
    (0, common_1.Post)('test'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "testEmail", null);
__decorate([
    (0, common_1.Post)('run-tests'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "runEmailQueueTests", null);
__decorate([
    (0, common_1.Post)('test-rate-limiting'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EmailQueueController.prototype, "testRateLimiting", null);
EmailQueueController = __decorate([
    (0, common_1.Controller)('email-queue'),
    __metadata("design:paramtypes", [enhanced_email_service_1.EnhancedEmailService,
        email_queue_test_service_1.EmailQueueTestService])
], EmailQueueController);
exports.EmailQueueController = EmailQueueController;
//# sourceMappingURL=email-queue.controller.js.map