"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailQueueModule = void 0;
const common_1 = require("@nestjs/common");
const bull_1 = require("@nestjs/bull");
const email_queue_service_1 = require("./email-queue.service");
const email_processor_1 = require("./email.processor");
const enhanced_email_service_1 = require("./enhanced-email.service");
const email_queue_controller_1 = require("./email-queue.controller");
const email_queue_test_service_1 = require("./email-queue.test.service");
const redis_config_1 = require("../../config/redis.config");
let EmailQueueModule = class EmailQueueModule {
};
EmailQueueModule = __decorate([
    (0, common_1.Module)({
        imports: [
            bull_1.BullModule.forRootAsync({
                useFactory: () => ({
                    redis: {
                        host: process.env.REDIS_HOST || 'localhost',
                        port: parseInt(process.env.REDIS_PORT || '6379'),
                        password: process.env.REDIS_PASSWORD,
                        db: parseInt(process.env.REDIS_DB || '0'),
                        lazyConnect: true,
                        maxRetriesPerRequest: 3,
                    },
                }),
            }),
            bull_1.BullModule.registerQueue({
                name: 'email-queue',
                defaultJobOptions: {
                    removeOnComplete: 100,
                    removeOnFail: 50,
                    attempts: 3,
                    backoff: {
                        type: 'exponential',
                        delay: 2000,
                    },
                },
            }),
        ],
        controllers: [email_queue_controller_1.EmailQueueController],
        providers: [email_queue_service_1.EmailQueueService, email_processor_1.EmailProcessor, enhanced_email_service_1.EnhancedEmailService, email_queue_test_service_1.EmailQueueTestService, redis_config_1.RedisService],
        exports: [email_queue_service_1.EmailQueueService, enhanced_email_service_1.EnhancedEmailService, email_queue_test_service_1.EmailQueueTestService],
    })
], EmailQueueModule);
exports.EmailQueueModule = EmailQueueModule;
//# sourceMappingURL=email-queue.module.js.map