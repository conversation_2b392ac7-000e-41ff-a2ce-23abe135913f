{"version": 3, "file": "email-queue-usage.example.js", "sourceRoot": "", "sources": ["../../../src/examples/email-queue-usage.example.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,0FAAqF;AAO9E,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IACjC,YAAoB,oBAA0C;QAA1C,yBAAoB,GAApB,oBAAoB,CAAsB;IAAG,CAAC;IAKlE,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;YACxC,EAAE,EAAE,kBAAkB;YACtB,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE,mDAAmD;YACzD,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,SAAS;SACrB,CAAC,CAAC;IACL,CAAC;IAMD,KAAK,CAAC,cAAc;QAClB,MAAM,MAAM,GAAG,EAAE,CAAC;QAGlB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE;YAC7B,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,OAAO,CAAC,cAAc;gBAC1B,OAAO,EAAE,eAAe,CAAC,EAAE;gBAC3B,IAAI,EAAE,mDAAmD,CAAC,MAAM;gBAChE,QAAQ,EAAE,KAAc;gBACxB,SAAS,EAAE,YAAY;aACxB,CAAC,CAAC;SACJ;QAGD,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QACvD,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;IACpE,CAAC;IAKD,KAAK,CAAC,kBAAkB;QACtB,MAAM,IAAI,CAAC,oBAAoB,CAAC,kBAAkB,CAChD,oBAAoB,EACpB,cAAc,EACd;YACE,IAAI,EAAE,UAAU;YAChB,YAAY,EAAE,WAAW;YACzB,IAAI,EAAE,gCAAgC;SACvC,EACD,4BAA4B,EAC5B,IAAI,EACJ,MAAM,CACP,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;YACxC,EAAE,EAAE,mBAAmB;YACvB,OAAO,EAAE,sBAAsB;YAC/B,IAAI,EAAE,2DAA2D;YACjE,QAAQ,EAAE,UAAU;YACpB,SAAS,EAAE,OAAO;SACnB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,kBAAkB;QACtB,MAAM,iBAAiB,GAAG;YACxB,IAAI,EAAE;gBACJ,IAAI,EAAE,gBAAgB;gBACtB,IAAI,EAAE,GAAG;gBACT,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAsB;oBAC5B,IAAI,EAAE,mBAAmB;iBAC1B;gBACD,IAAI,EAAE,mBAAmB;aAC1B;SACF,CAAC;QAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;YACxC,EAAE,EAAE,oBAAoB;YACxB,OAAO,EAAE,gCAAgC;YACzC,IAAI,EAAE,uDAAuD;YAC7D,GAAG,EAAE,iBAAiB;YACtB,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,aAAa;SACzB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;YACrC,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,KAAK,EAAE,KAAK,CAAC,KAAK;SACnB,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,qBAAqB;QACzB,MAAM,UAAU,GAAG;YACjB,uBAAuB;YACvB,uBAAuB;YACvB,uBAAuB;SAExB,CAAC;QAEF,MAAM,WAAW,GAAG;;;;KAInB,CAAC;QAEF,MAAM,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CACjD,UAAU,EACV,+BAA+B,EAC/B,WAAW,CACZ,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,WAAW;QAEf,MAAM,IAAI,CAAC,oBAAoB,CAAC,eAAe,EAAE,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;QAGlC,MAAM,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,EAAE,CAAC;QACnD,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IAIrC,CAAC;IAKD,KAAK,CAAC,eAAe;QACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC;YAC1C,KAAK,EAAE,kBAAkB;YACzB,IAAI,EAAE;gBACJ,IAAI,EAAE,UAAU;gBAChB,YAAY,EAAE,WAAW;gBACzB,IAAI,EAAE,+BAA+B;aACtC;YACD,QAAQ,EAAE,gBAAgB;YAC1B,OAAO,EAAE,wBAAwB;YACjC,GAAG,EAAE,gBAAgB;YACrB,EAAE,EAAE,GAAG;SACR,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAvKY,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAE+B,6CAAoB;GADnD,sBAAsB,CAuKlC;AAvKY,wDAAsB"}